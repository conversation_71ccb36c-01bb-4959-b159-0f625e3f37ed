#!/usr/bin/env python3

import csv
import subprocess
import time
import threading
from datetime import datetime
import os
from collections import deque

# Configuration - adjust these for your Raspberry Pi
SWEEP_COMMAND = "sudo hackrf_sweep -f 2400:2483 -w 100000 -1"  # Wider bins for Pi performance
UPDATE_INTERVAL = 5  # seconds between sweeps
JAMMING_THRESHOLD = -50  # dBm
CONSISTENT_JAMMING_COUNT = 3

# WiFi channels to monitor
WIFI_CHANNELS = {
    1: {"center": 2412, "bandwidth": 22},
    6: {"center": 2437, "bandwidth": 22},
    11: {"center": 2462, "bandwidth": 22}
}

class SpectrumMonitor:
    def __init__(self):
        self.channel_stats = {ch: {
            "history": deque(maxlen=10),
            "jamming_count": 0,
            "current_power": -100
        } for ch in WIFI_CHANNELS}
        self.running = True
        self.lock = threading.Lock()
        self.last_update = "Never"
        
    def run_sweep(self):
        """Collect spectrum data periodically"""
        while self.running:
            try:
                # Create temp file for sweep data
                temp_file = f"temp_sweep_{os.getpid()}.csv"
                
                # Run sweep command (timeout after 15 seconds)
                cmd = f"{SWEEP_COMMAND} -r {temp_file}"
                subprocess.run(cmd, shell=True, check=True, timeout=15)
                
                # Process the data
                with self.lock:
                    self.process_sweep_data(temp_file)
                    self.last_update = datetime.now().strftime('%H:%M:%S')
                
                # Clean up
                if os.path.exists(temp_file):
                    os.remove(temp_file)
                    
            except subprocess.TimeoutExpired:
                print("Warning: Sweep timed out")
            except Exception as e:
                print(f"Sweep error: {str(e)}")
                
            time.sleep(UPDATE_INTERVAL)
    
    def process_sweep_data(self, filename):
        """Analyze the sweep data for each WiFi channel"""
        try:
            with open(filename, 'r') as f:
                reader = csv.reader(f)
                freqs = []
                powers = []
                
                for row in reader:
                    if len(row) < 7:
                        continue
                    try:
                        start_freq = float(row[2]) / 1e6  # Convert to MHz
                        bin_width = float(row[3]) / 1e6
                        dbm_values = [float(x) for x in row[6:] if x.strip()]
                        
                        # Collect all frequency/power pairs
                        for i, power in enumerate(dbm_values):
                            freqs.append(start_freq + i * bin_width)
                            powers.append(power)
                    except (ValueError, IndexError):
                        continue
                
                # Analyze each WiFi channel
                for channel, info in WIFI_CHANNELS.items():
                    center = info["center"]
                    lower = center - info["bandwidth"]/2
                    upper = center + info["bandwidth"]/2
                    
                    # Get power readings within this channel
                    channel_powers = [
                        p for f, p in zip(freqs, powers)
                        if lower <= f <= upper
                    ]
                    
                    if not channel_powers:
                        stats = {"avg": -100, "max": -100, "pct": 0}
                    else:
                        stats = {
                            "avg": sum(channel_powers)/len(channel_powers),
                            "max": max(channel_powers),
                            "pct": sum(p > JAMMING_THRESHOLD for p in channel_powers)/len(channel_powers)*100
                        }
                    
                    # Update channel stats
                    self.channel_stats[channel]["history"].append(stats)
                    self.channel_stats[channel]["current_power"] = stats["avg"]
                    
                    # Jamming detection logic
                    if stats["pct"] > 70 or stats["max"] > -30:
                        self.channel_stats[channel]["jamming_count"] += 1
                    else:
                        self.channel_stats[channel]["jamming_count"] = 0
                        
        except Exception as e:
            print(f"Data processing error: {str(e)}")
    
    def get_status_report(self):
        """Generate terminal status display"""
        report = []
        for channel, stats in self.channel_stats.items():
            is_jamming = stats["jamming_count"] >= CONSISTENT_JAMMING_COUNT
            status = "\033[91mJAMMING!\033[0m" if is_jamming else "\033[92mNormal\033[0m"
            power = stats["current_power"]
            
            report.append(
                f"Channel {channel:2d}: {power:6.1f} dBm | Status: {status}"
            )
        
        return (
            f"\nWiFi Spectrum Monitor (Last Update: {self.last_update})\n"
            f"{'-' * 50}\n"
            + "\n".join(report)
            + "\n" + "-" * 50
            + "\nPress Ctrl+C to exit\n"
        )
    
    def stop(self):
        self.running = False

def clear_screen():
    """Clear terminal screen"""
    os.system('clear')

def main():
    # Verify HackRF is connected
    try:
        subprocess.run(["sudo hackrf_info"], check=True, 
                      stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    except:
        print("Error: HackRF not detected!")
        return
    
    monitor = SpectrumMonitor()
    sweep_thread = threading.Thread(target=monitor.run_sweep)
    sweep_thread.daemon = True
    sweep_thread.start()
    
    try:
        while True:
            clear_screen()
            with monitor.lock:
                print(monitor.get_status_report())
            
            # Check for jamming and beep if detected
            if any(stats["jamming_count"] >= CONSISTENT_JAMMING_COUNT 
                  for stats in monitor.channel_stats.values()):
                print("\a", end='', flush=True)  # System beep
            
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\nStopping monitor...")
    finally:
        monitor.stop()
        sweep_thread.join()

if __name__ == "__main__":
    main()
