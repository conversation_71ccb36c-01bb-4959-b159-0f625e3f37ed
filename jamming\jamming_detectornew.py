#!/usr/bin/env python3

import csv
import matplotlib.pyplot as plt
import numpy as np
import threading
import time
import subprocess
from datetime import datetime
import os
import re
import paramiko
from collections import deque
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("jamming_detector.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Configuration
SWEEP_COMMAND = "hackrf_sweep -f 2400:2483 -w 50000 -1"  # More data points with smaller bins
UPDATE_INTERVAL = 5  # seconds between sweeps
JAMMING_THRESHOLD = -55  # dBm (threshold for jamming detection)
CONSISTENT_JAMMING_COUNT = 2  # Number of consecutive detections to confirm jamming

# WiFi channel information for 2.4GHz
WIFI_CHANNELS = {
    1: {"center": 2412, "bandwidth": 22, "color": "#FF6B6B"},  # Channel 1 (2401-2423 MHz)
    2: {"center": 2417, "bandwidth": 22, "color": "#FF8E72"},
    3: {"center": 2422, "bandwidth": 22, "color": "#FFA579"},
    4: {"center": 2427, "bandwidth": 22, "color": "#FFB77F"},
    5: {"center": 2432, "bandwidth": 22, "color": "#FFD285"},
    6: {"center": 2437, "bandwidth": 22, "color": "#4ECDC4"},  # Channel 6 (2426-2448 MHz)
    7: {"center": 2442, "bandwidth": 22, "color": "#5CD1B9"},
    8: {"center": 2447, "bandwidth": 22, "color": "#6AD6AE"},
    9: {"center": 2452, "bandwidth": 22, "color": "#78DBA3"},
    10: {"center": 2457, "bandwidth": 22, "color": "#86E098"},
    11: {"center": 2462, "bandwidth": 22, "color": "#FFBE0B"},  # Channel 11 (2451-2473 MHz)
    12: {"center": 2467, "bandwidth": 22, "color": "#FFD23F"},
    13: {"center": 2472, "bandwidth": 22, "color": "#FFE573"}
}

# Primary non-overlapping channels (commonly used)
PRIMARY_CHANNELS = [1, 6, 11]

# OpenWRT Router Configuration
OPENWRT_IP = "**********"
OPENWRT_USER = "root"
OPENWRT_PASSWORD = "fontys123"
OPENWRT_WIRELESS_CONFIG = "/etc/config/wireless"
OPENWRT_AP_NAME = "Fontys_AP_Jamming"
CHANNEL_SWITCH_COOLDOWN = 60  # seconds between channel switches

class JammingDetector:
    def __init__(self):
        """Initialize the jamming detector with necessary data structures"""
        self.freqs = []
        self.powers = []
        self.channel_stats = {ch: {"history": deque(maxlen=10), "jamming_count": 0, "state": "normal"}
                             for ch in WIFI_CHANNELS}
        self.running = True
        self.lock = threading.Lock()
        self.last_update = time.time()

        # OpenWRT AP monitoring
        self.ap_channel = None
        self.last_channel_check = 0
        self.last_channel_switch = 0
        self.channel_switch_history = []

    def run_sweep(self):
        """Thread function to run frequency sweeps periodically"""
        while self.running:
            try:
                # Create a unique temporary file for this sweep
                temp_file = f"temp_{datetime.now().strftime('%H%M%S')}.csv"
                cmd = f"{SWEEP_COMMAND} -r {temp_file}"

                logger.debug(f"Running sweep command: {cmd}")
                subprocess.run(cmd, shell=True, check=True, timeout=15)

                with self.lock:
                    new_freqs, new_powers = self.load_sweep_data(temp_file)
                    if new_freqs and len(new_freqs) > 10:  # Ensure sufficient data
                        # Sort by frequency to ensure proper line connections
                        sorted_indices = np.argsort(new_freqs)
                        self.freqs = np.array(new_freqs)[sorted_indices].tolist()
                        self.powers = np.array(new_powers)[sorted_indices].tolist()
                        self.analyze_channels()
                        self.last_update = time.time()

                        # Check and mitigate AP jamming if needed
                        self.check_and_mitigate_ap_jamming()
                    else:
                        logger.warning("Insufficient data points collected in sweep")

                # Clean up temporary file
                os.remove(temp_file)
            except subprocess.TimeoutExpired:
                logger.error("Sweep command timed out")
            except subprocess.CalledProcessError as e:
                logger.error(f"Sweep command failed with return code {e.returncode}")
            except Exception as e:
                logger.error(f"Sweep error: {str(e)}")

            # Wait before next sweep
            time.sleep(UPDATE_INTERVAL)

    def load_sweep_data(self, filename):
        """Load and parse sweep data from CSV file"""
        freqs, powers = [], []
        try:
            with open(filename, 'r') as f:
                reader = csv.reader(f)
                for row in reader:
                    if len(row) < 7:  # Skip invalid rows
                        continue
                    try:
                        start_freq = float(row[2])  # Start frequency in Hz
                        bin_width = float(row[3])   # Bin width in Hz
                        dbm_values = [float(x) for x in row[6:] if x.strip()]

                        # Calculate frequency for each power measurement
                        freqs.extend([start_freq + i*bin_width for i in range(len(dbm_values))])
                        powers.extend(dbm_values)
                    except (ValueError, IndexError) as e:
                        logger.debug(f"Skipping invalid row: {e}")
                        continue
        except Exception as e:
            logger.error(f"Data loading error: {str(e)}")

        return freqs, powers

    def analyze_channels(self):
        """Analyze each WiFi channel for jamming"""
        if not self.freqs:
            logger.warning("No frequency data available for analysis")
            return

        # Convert frequencies to MHz for easier comparison with WiFi channels
        freqs_mhz = np.array(self.freqs) / 1e6
        powers = np.array(self.powers)

        for channel, info in WIFI_CHANNELS.items():
            center = info["center"]
            lower = center - info["bandwidth"]/2
            upper = center + info["bandwidth"]/2

            # Get power readings within this channel's frequency range
            mask = (freqs_mhz >= lower) & (freqs_mhz <= upper)
            channel_pwr = powers[mask]

            if len(channel_pwr) == 0:
                # No data points in this channel range
                self.channel_stats[channel]["history"].append({
                    "avg": -100,
                    "max": -100,
                    "above_threshold": 0
                })
                continue

            # Calculate channel statistics
            stats = {
                "avg": np.mean(channel_pwr),
                "max": np.max(channel_pwr),
                "above_threshold": np.mean(channel_pwr > JAMMING_THRESHOLD) * 100  # Percentage above threshold
            }
            self.channel_stats[channel]["history"].append(stats)

            # Jamming detection based on -55dBm threshold
            if stats["max"] > JAMMING_THRESHOLD:
                self.channel_stats[channel]["jamming_count"] += 1
                if self.channel_stats[channel]["jamming_count"] >= CONSISTENT_JAMMING_COUNT:
                    if self.channel_stats[channel]["state"] == "normal":
                        logger.warning(f"JAMMING DETECTED on Channel {channel} (max power: {stats['max']:.1f} dBm)")
                    self.channel_stats[channel]["state"] = "jammed"
            else:
                # Reset jamming counter if signal drops below threshold
                self.channel_stats[channel]["jamming_count"] = 0
                if self.channel_stats[channel]["state"] == "jammed":
                    logger.info(f"Channel {channel} returned to normal state")
                self.channel_stats[channel]["state"] = "normal"

    def get_channel_report(self):
        """Generate channel status report for display"""
        report = []
        for channel, info in WIFI_CHANNELS.items():
            try:
                if not self.channel_stats[channel]["history"]:
                    report.append(f"Channel {channel}: No data")
                    continue

                current = self.channel_stats[channel]["history"][-1]
                is_jammed = self.channel_stats[channel]["state"] == "jammed"
                status = "JAMMED" if is_jammed else "Normal"
                color = "\033[91m" if is_jammed else "\033[92m"  # Red for jammed, green for normal

                # Format the report line
                report.append(
                    f"{color}Ch{channel} ({info['center']}MHz): "
                    f"Avg={current['avg']:.1f}dBm Max={current['max']:.1f}dBm "
                    f"- {status}\033[0m"
                )
            except Exception as e:
                logger.error(f"Error generating report for channel {channel}: {str(e)}")
                report.append(f"Channel {channel}: Error generating report")
        return "\n".join(report)

    def is_channel_jammed(self, channel):
        """Check if a specific channel is currently jammed"""
        try:
            return self.channel_stats[channel]["state"] == "jammed"
        except Exception as e:
            logger.error(f"Error checking if channel {channel} is jammed: {str(e)}")
            return False  # Default to not jammed if there's an error

    def get_jammed_channels(self):
        """Return a list of all currently jammed channels"""
        jammed_channels = []
        for ch, stats in self.channel_stats.items():
            try:
                if stats["state"] == "jammed":
                    jammed_channels.append(ch)
            except Exception as e:
                logger.error(f"Error checking jammed state for channel {ch}: {str(e)}")
        return jammed_channels

    def get_openwrt_ap_channel(self):
        """Connect to OpenWRT router and get the current channel of Fontys_AP_Jamming"""
        try:
            # Create SSH client
            client = paramiko.SSHClient()
            client.set_missing_host_key_policy(paramiko.AutoAddPolicy())

            # Connect to the OpenWRT router
            logger.info(f"Connecting to OpenWRT router at {OPENWRT_IP}")
            client.connect(OPENWRT_IP, username=OPENWRT_USER, password=OPENWRT_PASSWORD)

            # Execute command to read wireless config
            cmd = f"cat {OPENWRT_WIRELESS_CONFIG}"
            _, stdout, _ = client.exec_command(cmd)
            config_content = stdout.read().decode('utf-8')

            # Close the connection
            client.close()

            # Parse the config to find the AP and its channel
            in_radio_section = False
            ap_radio_device = None
            channel = None

            for line in config_content.splitlines():
                line = line.strip()

                # Track which radio device we're in
                if line.startswith('config wifi-device'):
                    in_radio_section = True
                    current_radio = line.split("'")[1]
                    continue

                # Track which interface we're in
                if line.startswith('config wifi-iface'):
                    in_radio_section = False
                    continue

                # If we're in a radio section, look for channel
                if in_radio_section and line.startswith('option channel'):
                    channel = line.split("'")[1]
                    continue

                # Find which radio our AP is on
                if line.startswith('option ssid') and OPENWRT_AP_NAME in line:
                    # Look for the device this AP is using
                    for prev_line in config_content.splitlines():
                        if 'option device' in prev_line and prev_line.strip().startswith('option device'):
                            ap_radio_device = prev_line.split("'")[1]
                            break

            # Now find the channel for the AP's radio
            in_radio_section = False
            for line in config_content.splitlines():
                line = line.strip()

                if line.startswith('config wifi-device'):
                    current_radio = line.split("'")[1]
                    in_radio_section = (current_radio == ap_radio_device)
                    continue

                if in_radio_section and line.startswith('option channel'):
                    channel = int(line.split("'")[1])
                    break

            if channel is not None:
                logger.info(f"Found {OPENWRT_AP_NAME} operating on channel {channel}")
                self.ap_channel = channel
                return channel
            else:
                logger.error(f"Could not find channel for {OPENWRT_AP_NAME}")
                return None

        except Exception as e:
            logger.error(f"Error connecting to OpenWRT router: {str(e)}")
            return None

    def change_openwrt_ap_channel(self, new_channel):
        """Change the channel of the Fontys_AP_Jamming access point"""
        if new_channel == self.ap_channel:
            logger.info(f"AP already on channel {new_channel}, no change needed")
            return True

        try:
            # Create SSH client
            client = paramiko.SSHClient()
            client.set_missing_host_key_policy(paramiko.AutoAddPolicy())

            # Connect to the OpenWRT router
            logger.info(f"Connecting to OpenWRT router at {OPENWRT_IP}")
            client.connect(OPENWRT_IP, username=OPENWRT_USER, password=OPENWRT_PASSWORD)

            # First, find which radio device our AP is using
            cmd = f"cat {OPENWRT_WIRELESS_CONFIG}"
            _, stdout, _ = client.exec_command(cmd)
            config_content = stdout.read().decode('utf-8')

            # Parse to find the radio device for our AP
            ap_radio_device = None
            for line in config_content.splitlines():
                if line.strip().startswith('option ssid') and OPENWRT_AP_NAME in line:
                    # Look for the device this AP is using
                    for config_line in config_content.splitlines():
                        if 'option device' in config_line and config_line.strip().startswith('option device'):
                            ap_radio_device = config_line.split("'")[1]
                            break
                    break

            if not ap_radio_device:
                logger.error(f"Could not find radio device for {OPENWRT_AP_NAME}")
                client.close()
                return False

            # Use sed to change the channel in the config file
            sed_cmd = f"sed -i '/config wifi-device \'{ap_radio_device}\'/,/config wifi/ s/option channel \'[0-9]*\'/option channel \'{new_channel}\'/' {OPENWRT_WIRELESS_CONFIG}"
            _, stdout, stderr = client.exec_command(sed_cmd)

            # Check for errors
            err = stderr.read().decode('utf-8')
            if err:
                logger.error(f"Error changing channel: {err}")
                client.close()
                return False

            # Reload WiFi to apply changes
            logger.info(f"Reloading WiFi configuration on OpenWRT")
            _, stdout, _ = client.exec_command("wifi reload")

            # Wait for the command to complete
            exit_status = stdout.channel.recv_exit_status()

            # Close the connection
            client.close()

            if exit_status == 0:
                logger.info(f"Successfully changed {OPENWRT_AP_NAME} to channel {new_channel}")
                old_channel = self.ap_channel
                self.ap_channel = new_channel
                self.last_channel_switch = time.time()

                # Record the channel switch
                self.channel_switch_history.append({
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "old_channel": old_channel,
                    "new_channel": new_channel,
                    "reason": f"Jamming detected on channel {old_channel}"
                })

                return True
            else:
                logger.error("Failed to reload WiFi configuration")
                return False

        except Exception as e:
            logger.error(f"Error changing AP channel: {str(e)}")
            return False

    def check_and_mitigate_ap_jamming(self):
        """Check if the AP's channel is being jammed and change it if necessary"""
        # Only check periodically
        if time.time() - self.last_channel_check < 10:  # Check every 10 seconds
            return

        self.last_channel_check = time.time()

        # Get current AP channel if we don't have it
        if self.ap_channel is None:
            self.ap_channel = self.get_openwrt_ap_channel()
            if self.ap_channel is None:
                logger.warning("Could not determine AP channel, skipping mitigation check")
                return

        # Check if current AP channel is jammed
        if self.is_channel_jammed(self.ap_channel):
            logger.warning(f"AP channel {self.ap_channel} is being jammed!")

            # Only switch if we haven't recently switched (to avoid rapid switching)
            if time.time() - self.last_channel_switch > CHANNEL_SWITCH_COOLDOWN:
                # Find a non-jammed channel, preferably a primary one
                jammed_channels = self.get_jammed_channels()

                # First try primary channels
                available_primary = [ch for ch in PRIMARY_CHANNELS if ch not in jammed_channels]

                if available_primary:
                    # Choose the primary channel with lowest average power
                    best_channel = self.get_lowest_power_channel(available_primary)
                    logger.info(f"Switching to non-jammed primary channel {best_channel}")
                else:
                    # If all primary channels are jammed, try any non-jammed channel
                    available_channels = [ch for ch in WIFI_CHANNELS.keys() if ch not in jammed_channels]

                    if available_channels:
                        best_channel = self.get_lowest_power_channel(available_channels)
                        logger.info(f"All primary channels jammed. Switching to channel {best_channel}")
                    else:
                        # If all channels are jammed, pick the one with the lowest power
                        best_channel = self.get_lowest_power_channel(list(WIFI_CHANNELS.keys()))
                        logger.warning(f"All channels appear to be jammed! Selecting least jammed channel {best_channel}")

                # Change the AP channel
                success = self.change_openwrt_ap_channel(best_channel)
                if success:
                    logger.info(f"Successfully changed AP channel to {best_channel}")
                else:
                    logger.error("Failed to change AP channel")
            else:
                logger.info(f"Channel switch cooldown active, waiting before switching again")
        else:
            logger.debug(f"AP channel {self.ap_channel} is not currently jammed")

    def get_lowest_power_channel(self, channel_list):
        """Find the channel with the lowest average power from a list of channels"""
        lowest_power = float('inf')
        best_channel = None

        for channel in channel_list:
            if not self.channel_stats[channel]["history"]:
                continue

            avg_power = self.channel_stats[channel]["history"][-1]["avg"]
            if avg_power < lowest_power:
                lowest_power = avg_power
                best_channel = channel

        # If we couldn't determine (no history data), just pick the first channel
        if best_channel is None and channel_list:
            best_channel = channel_list[0]

        return best_channel

    def get_ap_status_report(self):
        """Generate a report about the AP status"""
        if self.ap_channel is None:
            return "AP Status: Unknown (not connected to OpenWRT)"

        is_jammed = self.is_channel_jammed(self.ap_channel)
        status = "JAMMED" if is_jammed else "Normal"
        color = "\033[91m" if is_jammed else "\033[92m"

        report = [f"AP Status: {color}{OPENWRT_AP_NAME} on Channel {self.ap_channel} - {status}\033[0m"]

        # Add recent channel switches
        if self.channel_switch_history:
            report.append("\nRecent channel switches:")
            for switch in self.channel_switch_history[-3:]:  # Show last 3 switches
                report.append(f"  {switch['timestamp']}: Channel {switch['old_channel']} → {switch['new_channel']}")

        return "\n".join(report)

    def stop(self):
        """Stop the detector"""
        logger.info("Stopping jamming detector")
        self.running = False

def clear_screen():
    """Clear terminal screen"""
    os.system('cls' if os.name == 'nt' else 'clear')

def display_realtime(detector):
    """Display real-time analysis with connected line plot"""
    use_gui = True
    try:
        plt.ion()  # Enable interactive mode
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10), gridspec_kw={'height_ratios': [3, 1]})

        # Create line plot for spectrum data
        line, = ax1.plot([], [], 'b-', linewidth=1, alpha=0.8, label='Signal')

        # Setup spectrum plot
        ax1.set_title("2.4GHz WiFi Spectrum Analysis", fontsize=14, pad=20)
        ax1.set_xlabel("Frequency (MHz)")
        ax1.set_ylabel("Power (dBm)")
        ax1.set_xlim(2400, 2483)
        ax1.set_ylim(-100, -20)
        ax1.grid(True, alpha=0.3)

        # Add threshold line
        ax1.axhline(y=JAMMING_THRESHOLD, color='r', linestyle='--', alpha=0.7,
                   label=f'Jamming Threshold ({JAMMING_THRESHOLD} dBm)')

        # Add channel markers
        for ch, info in WIFI_CHANNELS.items():
            try:
                # Highlight primary channels differently
                alpha = 0.3 if ch in PRIMARY_CHANNELS else 0.1
                ax1.axvspan(info["center"]-11, info["center"]+11,
                          color=info["color"], alpha=alpha)

                # Only label primary channels to avoid clutter
                if ch in PRIMARY_CHANNELS:
                    ax1.text(info["center"], -30, f"Ch{ch}",
                           ha='center', color=info["color"], weight='bold')
            except Exception as e:
                logger.error(f"Error adding channel {ch} marker: {str(e)}")

        # Setup channel status plot
        ax2.set_title("Channel Status", fontsize=12)
        ax2.set_xlim(0, 14)  # Channels 1-13
        ax2.set_ylim(0, 1)
        ax2.set_xlabel("Channel")
        ax2.set_yticks([])  # No y-ticks needed
        ax2.set_xticks(list(WIFI_CHANNELS.keys()))

        # Create initial channel status bars
        channel_bars = {}
        for ch in WIFI_CHANNELS:
            bar = ax2.bar(ch, 1, width=0.8, color='green', alpha=0.7)
            channel_bars[ch] = bar

        ax1.legend(loc='upper right')
        plt.tight_layout()

    except Exception as e:
        logger.error(f"Error initializing matplotlib display: {str(e)}")
        use_gui = False
        print("Running in terminal-only mode (no graphical display)")

    last_graph_update = 0

    while detector.running:
        try:
            clear_screen()
            print("\n=== WiFi Jamming Detection System ===")
            print(f"Last update: {datetime.fromtimestamp(detector.last_update).strftime('%H:%M:%S')}")

            with detector.lock:
                # Terminal output
                print("\nChannel Status:")
                print(detector.get_channel_report())

                # Show AP status
                print("\n" + detector.get_ap_status_report())

                jammed_channels = detector.get_jammed_channels()
                if jammed_channels:
                    print("\n\033[91m! ! ! JAMMING DETECTED ! ! !\033[0m")
                    print(f"Jammed channels: {', '.join(map(str, jammed_channels))}")

                    # Beep if AP channel is jammed
                    if detector.ap_channel and detector.is_channel_jammed(detector.ap_channel):
                        print("\a")  # Beep
        except Exception as e:
            logger.error(f"Error in display loop: {str(e)}")

            # Update graph every 2 seconds if we have data and GUI is enabled
            if use_gui and time.time() - last_graph_update > 2 and detector.freqs:
                try:
                    freqs_mhz = np.array(detector.freqs) / 1e6
                    powers = np.array(detector.powers)

                    # Update line plot
                    line.set_data(freqs_mhz, powers)

                    # Dynamic Y-axis scaling
                    y_min = min(powers)
                    y_max = max(powers)
                    ax1.set_ylim(max(-100, y_min-5), min(-20, y_max+10))

                    # Update channel status bars
                    for ch in WIFI_CHANNELS:
                        try:
                            is_jammed = detector.is_channel_jammed(ch)
                            color = 'red' if is_jammed else 'green'
                            channel_bars[ch][0].set_color(color)
                        except Exception as e:
                            logger.error(f"Error updating channel {ch} bar: {str(e)}")

                    fig.canvas.draw()
                    fig.canvas.flush_events()
                    last_graph_update = time.time()
                except Exception as e:
                    logger.error(f"Error updating plot: {str(e)}")
                    use_gui = False
                    print("Graphical display disabled due to error")

        time.sleep(0.5)

    if use_gui:
        try:
            plt.ioff()
            plt.close()
        except:
            pass

def check_hackrf():
    """Check if HackRF is connected and available"""
    try:
        # Run the command but don't capture the result since we only care if it succeeds
        subprocess.run(["hackrf_info"],
                      check=True,
                      stdout=subprocess.PIPE,
                      stderr=subprocess.PIPE)
        logger.info("HackRF device detected")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        logger.error("HackRF not detected or hackrf_tools not installed")
        return False

if __name__ == "__main__":
    logger.info("Starting WiFi Jamming Detection System")

    # Verify HackRF is connected
    if not check_hackrf():
        print("Error: HackRF not detected or hackrf_tools not installed!")
        exit(1)

    # Create and start the detector
    detector = JammingDetector()
    sweep_thread = threading.Thread(target=detector.run_sweep)
    sweep_thread.daemon = True
    sweep_thread.start()

    try:
        display_realtime(detector)
    except KeyboardInterrupt:
        print("\nStopping jamming detection system...")
    except Exception as e:
        logger.error(f"Error in display: {str(e)}")
    finally:
        detector.stop()
        sweep_thread.join(timeout=2)
        logger.info("Jamming detection system stopped")
