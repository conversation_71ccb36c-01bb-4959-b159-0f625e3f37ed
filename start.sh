#!/bin/bash

echo "Starting WiFi Anti-Jamming Monitor with Grafana and Prometheus..."
echo

echo "Installing Python dependencies..."
pip install -r requirements.txt
if [ $? -ne 0 ]; then
    echo "Failed to install Python dependencies"
    exit 1
fi

echo
echo "Starting Docker containers..."
docker-compose up -d
if [ $? -ne 0 ]; then
    echo "Failed to start Docker containers"
    exit 1
fi

echo
echo "Waiting for services to start..."
sleep 10

echo
echo "Starting Python monitoring application..."
echo
echo "Services will be available at:"
echo "- Grafana Dashboard: http://localhost:3000 (admin/admin)"
echo "- Prometheus: http://localhost:9090"
echo "- Metrics Endpoint: http://localhost:8000/metrics"
echo
echo "Press Ctrl+C to stop the monitoring application"
echo

python3 anti_jamming_windows.py
