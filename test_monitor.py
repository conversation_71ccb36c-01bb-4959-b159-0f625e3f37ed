from scapy.all import sniff, Dot11Beacon

INTERFACE = "\\Device\\NPF_{5810E8D4-87A0-4C02-9C39-794FBA03E99E}"  # WNA1100 GUID

def packet_handler(pkt):
    if pkt.haslayer(Dot11Beacon):
        try:
            rssi = pkt.dBm_AntSignal
            print(f"Beacon frame detected - RSSI: {rssi} dBm")
        except AttributeError:
            pass

sniff(iface=INTERFACE, prn=packet_handler, count=10, monitor=True)