#!/usr/bin/env python3
import time
import logging
import random
from threading import Thread
import socket
from prometheus_client import start_http_server, Gauge, Counter, Histogram, Info

# Configure logging
logging.basicConfig(
    filename='anti_jamming.log',
    level=logging.INFO,
    format='%(asctime)s: %(message)s'
)

# Settings
CHANNELS = [1, 6, 11]                      # 2.4 GHz channels
RSSI_THRESHOLD = -70                       # Minimum RSSI (dBm)
CHECK_INTERVAL = 2                         # Check every 2 seconds
SIMULATION_MODE = True                     # Use simulated data = True, Use real data = False
METRICS_PORT = 8000                        # Prometheus metrics port

# Simulation settings
SIM_BASE_RSSI = -60
SIM_VARIANCE = 5
SIM_JAMMING_PROBABILITY = 0.05

# Prometheus metrics
rssi_gauge = Gauge('wifi_rssi_dbm', 'WiFi RSSI in dBm', ['channel'])
current_channel_gauge = Gauge('wifi_current_channel', 'Current WiFi channel')
jamming_detected_counter = Counter('wifi_jamming_detected_total', 'Total jamming detections', ['channel'])
channel_switches_counter = Counter('wifi_channel_switches_total', 'Total channel switches')
rssi_histogram = Histogram('wifi_rssi_distribution', 'Distribution of RSSI values', ['channel'])
system_info = Info('wifi_monitor_info', 'WiFi monitoring system information')

def find_available_port(start_port=8000, max_port=8100):
    """Find an available port by trying a range of ports."""
    for port in range(start_port, max_port):
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            try:
                s.bind(('', port))
                return port
            except OSError:
                continue
    return None

def start_metrics_server():
    """Start Prometheus metrics server."""
    global METRICS_PORT

    # Try to find an available port
    available_port = find_available_port(METRICS_PORT)
    if available_port:
        METRICS_PORT = available_port
    else:
        logging.error("Could not find an available port for metrics")
        print("Error: Could not find an available port for metrics")
        return

    try:
        # Set system information
        system_info.info({
            'version': '2.0',
            'mode': 'simulation' if SIMULATION_MODE else 'real',
            'channels': ','.join(map(str, CHANNELS)),
            'rssi_threshold': str(RSSI_THRESHOLD)
        })

        start_http_server(METRICS_PORT)
        logging.info(f"Prometheus metrics server started at port {METRICS_PORT}")
        print(f"Prometheus metrics server started at port {METRICS_PORT}")
        print(f"Metrics available at http://localhost:{METRICS_PORT}/metrics")
    except Exception as e:
        logging.error(f"Metrics server error: {e}")
        print(f"Metrics server error: {e}")

def simulate_rssi(channel):
    """Generate simulated RSSI values."""
    # Simulate normal conditions
    rssi = SIM_BASE_RSSI + random.uniform(-SIM_VARIANCE, SIM_VARIANCE)

    # Occasionally simulate jamming
    is_jamming = random.random() < SIM_JAMMING_PROBABILITY
    if is_jamming:
        rssi = RSSI_THRESHOLD - random.uniform(5, 15)
        logging.warning(f"Simulated jamming on channel {channel}")

    return rssi, is_jamming

def main():
    """Monitor RSSI (real or simulated) and switch channels if jamming detected."""
    # Start metrics server in a separate thread
    metrics_thread = Thread(target=start_metrics_server, daemon=True)
    metrics_thread.start()

    # Give the server a moment to start
    time.sleep(1)

    current_channel_idx = 0
    current_channel = CHANNELS[current_channel_idx]
    current_channel_gauge.set(current_channel)

    logging.info(f"Started monitoring on channel {current_channel}")
    print(f"WiFi Anti-Jamming Monitor started")
    print(f"Prometheus metrics available at http://localhost:{METRICS_PORT}/metrics")
    print(f"Configure Grafana to scrape metrics from this endpoint")

    while True:
        for channel in CHANNELS:
            if SIMULATION_MODE:
                rssi, is_jamming = simulate_rssi(channel)
            else:
                # Original code for real monitoring would go here
                rssi = None
                is_jamming = False

            if rssi is not None:
                # Update Prometheus metrics
                rssi_gauge.labels(channel=str(channel)).set(rssi)
                rssi_histogram.labels(channel=str(channel)).observe(rssi)

                if is_jamming:
                    jamming_detected_counter.labels(channel=str(channel)).inc()

                # Log channel status
                logging.info(f"Channel {channel}, RSSI: {rssi:.1f} dBm, Jamming: {is_jamming}")

                # Check if current channel is jammed
                if channel == current_channel and rssi < RSSI_THRESHOLD:
                    # Switch channel
                    current_channel_idx = (current_channel_idx + 1) % len(CHANNELS)
                    current_channel = CHANNELS[current_channel_idx]
                    current_channel_gauge.set(current_channel)
                    channel_switches_counter.inc()

                    logging.info(f"Switching to channel {current_channel}")
                    print(f"Switching to channel {current_channel} due to jamming")

        time.sleep(CHECK_INTERVAL)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        logging.info("Stopped by user")
        print("\nMonitor stopped by user")
    except Exception as e:
        logging.error(f"Error: {e}")
        print(f"Error: {e}")
