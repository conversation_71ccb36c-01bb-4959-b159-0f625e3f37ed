@echo off
echo Starting WiFi Anti-Jamming Monitor with Grafana and Prometheus...
echo.

echo Installing Python dependencies...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo Failed to install Python dependencies
    pause
    exit /b 1
)

echo.
echo Starting Docker containers...
docker-compose up -d
if %errorlevel% neq 0 (
    echo Failed to start Docker containers
    pause
    exit /b 1
)

echo.
echo Waiting for services to start...
timeout /t 10 /nobreak > nul

echo.
echo Starting Python monitoring application...
echo.
echo Services will be available at:
echo - Grafana Dashboard: http://localhost:3000 (admin/admin)
echo - Prometheus: http://localhost:9090
echo - Metrics Endpoint: http://localhost:8000/metrics
echo.
echo Press Ctrl+C to stop the monitoring application
echo.

python anti_jamming_windows.py
