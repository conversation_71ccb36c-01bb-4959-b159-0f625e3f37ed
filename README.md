# WiFi Anti-Jamming Monitor with Grafana & Prometheus

A WiFi monitoring system that detects jamming attacks and automatically switches channels, with real-time visualization using Grafana and Prometheus.

## Features

- **Real-time RSSI monitoring** across multiple WiFi channels
- **Automatic channel switching** when jamming is detected
- **Prometheus metrics exposition** for monitoring and alerting
- **Grafana dashboard** for visualization
- **Simulation mode** for testing and demonstration
- **Docker-based deployment** for easy setup

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│                 │    │                 │    │                 │
│  Python App     │───▶│   Prometheus    │───▶│    Grafana      │
│  (Port 8000)    │    │   (Port 9090)   │    │   (Port 3000)   │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Quick Start

### Prerequisites

- Python 3.7+
- Docker and Docker Compose
- Git

### 1. Install Python Dependencies

```bash
pip install -r requirements.txt
```

### 2. Start the Monitoring Application

```bash
python anti_jamming_windows.py
```

The application will start and expose Prometheus metrics on port 8000.

### 3. Start Grafana and Prometheus

```bash
docker-compose up -d
```

This will start:
- **Prometheus** on http://localhost:9090
- **Grafana** on http://localhost:3000

### 4. Access the Dashboard

1. Open Grafana at http://localhost:3000
2. Login with:
   - Username: `admin`
   - Password: `admin`
3. The WiFi Anti-Jamming dashboard should be automatically available

## Metrics

The application exposes the following Prometheus metrics:

| Metric Name | Type | Description | Labels |
|-------------|------|-------------|--------|
| `wifi_rssi_dbm` | Gauge | Current RSSI value in dBm | `channel` |
| `wifi_current_channel` | Gauge | Currently active channel | - |
| `wifi_jamming_detected_total` | Counter | Total jamming detections | `channel` |
| `wifi_channel_switches_total` | Counter | Total channel switches | - |
| `wifi_rssi_distribution` | Histogram | Distribution of RSSI values | `channel` |
| `wifi_monitor_info` | Info | System information | - |

## Configuration

### Application Settings

Edit `anti_jamming_windows.py` to modify:

```python
CHANNELS = [1, 6, 11]                      # WiFi channels to monitor
RSSI_THRESHOLD = -70                       # Jamming detection threshold (dBm)
CHECK_INTERVAL = 2                         # Monitoring interval (seconds)
SIMULATION_MODE = True                     # Enable/disable simulation
METRICS_PORT = 8000                        # Prometheus metrics port
```

### Prometheus Configuration

The `prometheus.yml` file configures:
- Scrape interval: 5 seconds for WiFi metrics
- Target: `host.docker.internal:8000` (Windows/Mac) or `localhost:8000` (Linux)

### Grafana Dashboard

The dashboard includes:
- **RSSI Time Series**: Real-time RSSI values for all channels
- **Current Channel**: Display of the active channel
- **Jamming Events**: Counter of jamming detections
- **Channel Switches**: Total number of channel switches

## Simulation Mode

By default, the application runs in simulation mode, generating:
- **Normal RSSI values**: Around -60 dBm with ±5 dBm variance
- **Jamming events**: 5% probability per measurement
- **Automatic channel switching**: When RSSI drops below -70 dBm

## Real Hardware Integration

To use with real WiFi hardware, set `SIMULATION_MODE = False` and implement the hardware-specific RSSI measurement code in the main monitoring loop.

## Troubleshooting

### Port Conflicts

If ports are already in use:
- **Python app**: Automatically finds available port starting from 8000
- **Prometheus**: Change port in `docker-compose.yml`
- **Grafana**: Change port in `docker-compose.yml`

### Docker Issues

```bash
# Check container status
docker-compose ps

# View logs
docker-compose logs prometheus
docker-compose logs grafana

# Restart services
docker-compose restart
```

### Metrics Not Appearing

1. Verify Python app is running and metrics are available:
   ```bash
   curl http://localhost:8000/metrics
   ```

2. Check Prometheus targets:
   - Go to http://localhost:9090/targets
   - Ensure `wifi-anti-jamming` target is UP

3. Verify Grafana data source:
   - Go to Configuration > Data Sources
   - Test the Prometheus connection

## Development

### Adding New Metrics

1. Define the metric in the Python application:
   ```python
   new_metric = Gauge('wifi_new_metric', 'Description', ['label'])
   ```

2. Update the metric in your monitoring loop:
   ```python
   new_metric.labels(label='value').set(measurement)
   ```

3. Add visualization to the Grafana dashboard

### Custom Dashboards

Create additional dashboards by:
1. Designing in Grafana UI
2. Exporting JSON configuration
3. Adding to provisioning configuration

## License

This project is open source and available under the MIT License.
