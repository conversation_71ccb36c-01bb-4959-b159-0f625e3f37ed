#!/usr/bin/env python3

import csv
import matplotlib.pyplot as plt
import numpy as np
import threading
import time
import subprocess
from datetime import datetime
import os
from collections import deque

# Configuration
SWEEP_COMMAND = "hackrf_sweep -f 2400:2483 -w 50000 -1"  # More data points with smaller bins
UPDATE_INTERVAL = 5  # seconds between sweeps
CSV_FILE = "sweep_output.csv"
JAMMING_THRESHOLD = -50  # dBm
CONSISTENT_JAMMING_COUNT = 3

# WiFi channel information
WIFI_CHANNELS = {
    1: {"center": 2412, "bandwidth": 22, "color": "#FF6B6B"},
    6: {"center": 2437, "bandwidth": 22, "color": "#4ECDC4"}, 
    11: {"center": 2462, "bandwidth": 22, "color": "#FFBE0B"}
}

class JammingDetector:
    def __init__(self):
        self.freqs = []
        self.powers = []
        self.channel_stats = {ch: {"history": deque(maxlen=10), "jamming_count": 0} for ch in WIFI_CHANNELS}
        self.running = True
        self.lock = threading.Lock()
        self.jamming_alerts = {ch: False for ch in WIFI_CHANNELS}
        self.last_update = time.time()
        
    def run_sweep(self):
        """Thread function to run sweeps periodically"""
        while self.running:
            try:
                temp_file = f"temp_{datetime.now().strftime('%H%M%S')}.csv"
                cmd = f"{SWEEP_COMMAND} -r {temp_file}"
                subprocess.run(cmd, shell=True, check=True, timeout=15)
                
                with self.lock:
                    new_freqs, new_powers = self.load_sweep_data(temp_file)
                    if new_freqs and len(new_freqs) > 10:  # Ensure sufficient data
                        # Sort by frequency to ensure proper line connections
                        sorted_indices = np.argsort(new_freqs)
                        self.freqs = np.array(new_freqs)[sorted_indices].tolist()
                        self.powers = np.array(new_powers)[sorted_indices].tolist()
                        self.analyze_channels()
                        self.last_update = time.time()
                    
                os.remove(temp_file)
            except Exception as e:
                print(f"Sweep error: {e}")
            time.sleep(UPDATE_INTERVAL)

    def load_sweep_data(self, filename):
        """Load and sort sweep data"""
        freqs, powers = [], []
        try:
            with open(filename, 'r') as f:
                reader = csv.reader(f)
                for row in reader:
                    if len(row) < 7:
                        continue
                    try:
                        start_freq = float(row[2])
                        bin_width = float(row[3])
                        dbm_values = [float(x) for x in row[6:] if x.strip()]
                        freqs.extend([start_freq + i*bin_width for i in range(len(dbm_values))])
                        powers.extend(dbm_values)
                    except (ValueError, IndexError):
                        continue
        except Exception as e:
            print(f"Data loading error: {e}")
        return freqs, powers

    def analyze_channels(self):
        """Analyze each WiFi channel"""
        if not self.freqs:
            return
            
        freqs_mhz = np.array(self.freqs) / 1e6
        powers = np.array(self.powers)
        
        for channel, info in WIFI_CHANNELS.items():
            center = info["center"]
            lower = center - info["bandwidth"]/2
            upper = center + info["bandwidth"]/2
            
            mask = (freqs_mhz >= lower) & (freqs_mhz <= upper)
            channel_pwr = powers[mask]
            
            if len(channel_pwr) == 0:
                self.channel_stats[channel]["history"].append({"avg": -100, "max": -100, "pct": 0})
                continue
                
            stats = {
                "avg": np.mean(channel_pwr),
                "max": np.max(channel_pwr),
                "pct": np.mean(channel_pwr > JAMMING_THRESHOLD) * 100
            }
            self.channel_stats[channel]["history"].append(stats)
            
            # Jamming detection
            if stats["pct"] > 70 or stats["max"] > -30:
                self.channel_stats[channel]["jamming_count"] += 1
                if self.channel_stats[channel]["jamming_count"] >= CONSISTENT_JAMMING_COUNT:
                    self.jamming_alerts[channel] = True
            else:
                self.channel_stats[channel]["jamming_count"] = 0
                self.jamming_alerts[channel] = False

    def get_channel_report(self):
        """Generate channel status report"""
        report = []
        for channel, info in WIFI_CHANNELS.items():
            if not self.channel_stats[channel]["history"]:
                report.append(f"Channel {channel}: No data")
                continue
                
            current = self.channel_stats[channel]["history"][-1]
            status = "JAMMING!" if self.jamming_alerts[channel] else "Normal"
            color = "\033[91m" if self.jamming_alerts[channel] else "\033[92m"
            
            report.append(
                f"{color}Ch{channel} ({info['center']}MHz): "
                f"Avg={current['avg']:.1f}dB Max={current['max']:.1f}dB "
                f"- {status}\033[0m"
            )
        return "\n".join(report)

    def stop(self):
        self.running = False

def clear_screen():
    """Clear terminal screen"""
    os.system('cls' if os.name == 'nt' else 'clear')

def display_realtime(sweeper):
    """Display real-time analysis with connected line plot"""
    plt.ion()
    fig, ax = plt.subplots(figsize=(12,6))
    
    # Create line plot that will connect all points
    line, = ax.plot([], [], 'b-', linewidth=1, alpha=0.8, label='Signal')
    
    # Setup plot
    ax.set_title("2.4GHz WiFi Spectrum - Connected Points", pad=20)
    ax.set_xlabel("Frequency (MHz)")
    ax.set_ylabel("Power (dBm)")
    ax.set_xlim(2400, 2483)
    ax.set_ylim(-100, -20)
    ax.grid(True, alpha=0.3)
    
    # Add channel markers
    for ch, info in WIFI_CHANNELS.items():
        ax.axvspan(info["center"]-11, info["center"]+11, 
                  color=info["color"], alpha=0.1)
        ax.text(info["center"], -25, f"Ch{ch}", 
               ha='center', color=info["color"], weight='bold')
    
    last_graph_update = 0
    
    while sweeper.running:
        clear_screen()
        print("\n=== WiFi Jamming Detection ===")
        print(f"Last update: {datetime.fromtimestamp(sweeper.last_update).strftime('%H:%M:%S')}")
        print(f"Data points: {len(sweeper.freqs)}")
        print("\nChannel Status:")
        
        with sweeper.lock:
            # Terminal output
            print(sweeper.get_channel_report())
            
            if any(sweeper.jamming_alerts.values()):
                print("\n\033[91m! ! ! JAMMING DETECTED ! ! !\033[0m")
                print("\a")  # Beep
            
            # Update graph every 2 seconds if we have data
            if time.time() - last_graph_update > 2 and sweeper.freqs:
                freqs_mhz = np.array(sweeper.freqs) / 1e6
                powers = np.array(sweeper.powers)
                
                # Update line plot (automatically connects adjacent points)
                line.set_data(freqs_mhz, powers)
                
                # Dynamic Y-axis scaling
                y_min = min(powers)
                y_max = max(powers)
                ax.set_ylim(max(-100, y_min-5), min(-20, y_max+10))
                
                fig.canvas.draw()
                fig.canvas.flush_events()
                last_graph_update = time.time()
        
        time.sleep(0.5)
    
    plt.ioff()
    plt.close()

if __name__ == "__main__":
    # Verify HackRF is connected
    try:
        subprocess.run(["hackrf_info"], check=True, stdout=subprocess.PIPE)
    except:
        print("Error: HackRF not detected!")
        exit(1)

    sweeper = JammingDetector()
    sweep_thread = threading.Thread(target=sweeper.run_sweep)
    sweep_thread.daemon = True
    sweep_thread.start()
    
    try:
        display_realtime(sweeper)
    except KeyboardInterrupt:
        print("\nStopping...")
    finally:
        sweeper.stop()
        sweep_thread.join()
