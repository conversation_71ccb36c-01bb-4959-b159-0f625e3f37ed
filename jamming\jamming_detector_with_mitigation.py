#!/usr/bin/env python3

import csv
import matplotlib.pyplot as plt
import numpy as np
import threading
import time
import subprocess
from datetime import datetime
import os
from collections import deque
import logging
import random

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("jamming_detector.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Configuration
SWEEP_COMMAND = "hackrf_sweep -f 2400:2483 -w 50000 -1"  # More data points with smaller bins
UPDATE_INTERVAL = 5  # seconds between sweeps
JAMMING_THRESHOLD = -55  # dBm (threshold for jamming detection)
CONSISTENT_JAMMING_COUNT = 2  # Number of consecutive detections to confirm jamming

# WiFi channel information for 2.4GHz
WIFI_CHANNELS = {
    1: {"center": 2412, "bandwidth": 22, "color": "#FF6B6B"},  # Channel 1 (2401-2423 MHz)
    2: {"center": 2417, "bandwidth": 22, "color": "#FF8E72"},
    3: {"center": 2422, "bandwidth": 22, "color": "#FFA579"},
    4: {"center": 2427, "bandwidth": 22, "color": "#FFB77F"},
    5: {"center": 2432, "bandwidth": 22, "color": "#FFD285"},
    6: {"center": 2437, "bandwidth": 22, "color": "#4ECDC4"},  # Channel 6 (2426-2448 MHz)
    7: {"center": 2442, "bandwidth": 22, "color": "#5CD1B9"},
    8: {"center": 2447, "bandwidth": 22, "color": "#6AD6AE"},
    9: {"center": 2452, "bandwidth": 22, "color": "#78DBA3"},
    10: {"center": 2457, "bandwidth": 22, "color": "#86E098"},
    11: {"center": 2462, "bandwidth": 22, "color": "#FFBE0B"},  # Channel 11 (2451-2473 MHz)
    12: {"center": 2467, "bandwidth": 22, "color": "#FFD23F"},
    13: {"center": 2472, "bandwidth": 22, "color": "#FFE573"}
}

# Primary non-overlapping channels (commonly used)
PRIMARY_CHANNELS = [1, 6, 11]

class JammingDetector:
    def __init__(self):
        """Initialize the jamming detector with necessary data structures"""
        self.freqs = []
        self.powers = []
        self.channel_stats = {ch: {"history": deque(maxlen=10), "jamming_count": 0, "state": "normal"} 
                             for ch in WIFI_CHANNELS}
        self.running = True
        self.lock = threading.Lock()
        self.last_update = time.time()
        
        # Mitigation system variables
        self.current_channel = 1  # Default starting channel
        self.mitigation_active = False
        self.channel_switch_time = 0
        self.mitigation_history = []
        
    def run_sweep(self):
        """Thread function to run frequency sweeps periodically"""
        while self.running:
            try:
                # Create a unique temporary file for this sweep
                temp_file = f"temp_{datetime.now().strftime('%H%M%S')}.csv"
                cmd = f"{SWEEP_COMMAND} -r {temp_file}"
                
                logger.debug(f"Running sweep command: {cmd}")
                subprocess.run(cmd, shell=True, check=True, timeout=15)
                
                with self.lock:
                    new_freqs, new_powers = self.load_sweep_data(temp_file)
                    if new_freqs and len(new_freqs) > 10:  # Ensure sufficient data
                        # Sort by frequency to ensure proper line connections
                        sorted_indices = np.argsort(new_freqs)
                        self.freqs = np.array(new_freqs)[sorted_indices].tolist()
                        self.powers = np.array(new_powers)[sorted_indices].tolist()
                        self.analyze_channels()
                        self.last_update = time.time()
                        
                        # Run mitigation if needed
                        self.run_mitigation()
                    else:
                        logger.warning("Insufficient data points collected in sweep")
                
                # Clean up temporary file
                os.remove(temp_file)
            except subprocess.TimeoutExpired:
                logger.error("Sweep command timed out")
            except subprocess.CalledProcessError as e:
                logger.error(f"Sweep command failed with return code {e.returncode}")
            except Exception as e:
                logger.error(f"Sweep error: {str(e)}")
            
            # Wait before next sweep
            time.sleep(UPDATE_INTERVAL)

    def load_sweep_data(self, filename):
        """Load and parse sweep data from CSV file"""
        freqs, powers = [], []
        try:
            with open(filename, 'r') as f:
                reader = csv.reader(f)
                for row in reader:
                    if len(row) < 7:  # Skip invalid rows
                        continue
                    try:
                        start_freq = float(row[2])  # Start frequency in Hz
                        bin_width = float(row[3])   # Bin width in Hz
                        dbm_values = [float(x) for x in row[6:] if x.strip()]
                        
                        # Calculate frequency for each power measurement
                        freqs.extend([start_freq + i*bin_width for i in range(len(dbm_values))])
                        powers.extend(dbm_values)
                    except (ValueError, IndexError) as e:
                        logger.debug(f"Skipping invalid row: {e}")
                        continue
        except Exception as e:
            logger.error(f"Data loading error: {str(e)}")
        
        return freqs, powers

    def analyze_channels(self):
        """Analyze each WiFi channel for jamming"""
        if not self.freqs:
            logger.warning("No frequency data available for analysis")
            return
            
        # Convert frequencies to MHz for easier comparison with WiFi channels
        freqs_mhz = np.array(self.freqs) / 1e6
        powers = np.array(self.powers)
        
        for channel, info in WIFI_CHANNELS.items():
            center = info["center"]
            lower = center - info["bandwidth"]/2
            upper = center + info["bandwidth"]/2
            
            # Get power readings within this channel's frequency range
            mask = (freqs_mhz >= lower) & (freqs_mhz <= upper)
            channel_pwr = powers[mask]
            
            if len(channel_pwr) == 0:
                # No data points in this channel range
                self.channel_stats[channel]["history"].append({
                    "avg": -100, 
                    "max": -100, 
                    "above_threshold": 0
                })
                continue
                
            # Calculate channel statistics
            stats = {
                "avg": np.mean(channel_pwr),
                "max": np.max(channel_pwr),
                "above_threshold": np.mean(channel_pwr > JAMMING_THRESHOLD) * 100  # Percentage above threshold
            }
            self.channel_stats[channel]["history"].append(stats)
            
            # Jamming detection based on -55dBm threshold
            if stats["max"] > JAMMING_THRESHOLD:
                self.channel_stats[channel]["jamming_count"] += 1
                if self.channel_stats[channel]["jamming_count"] >= CONSISTENT_JAMMING_COUNT:
                    if self.channel_stats[channel]["state"] == "normal":
                        logger.warning(f"JAMMING DETECTED on Channel {channel} (max power: {stats['max']:.1f} dBm)")
                    self.channel_stats[channel]["state"] = "jammed"
            else:
                # Reset jamming counter if signal drops below threshold
                self.channel_stats[channel]["jamming_count"] = 0
                if self.channel_stats[channel]["state"] == "jammed":
                    logger.info(f"Channel {channel} returned to normal state")
                self.channel_stats[channel]["state"] = "normal"

    def run_mitigation(self):
        """Run the channel switching mitigation system"""
        # Check if current channel is jammed
        if self.is_channel_jammed(self.current_channel):
            # Only switch if we haven't recently switched (to avoid rapid switching)
            if not self.mitigation_active or (time.time() - self.channel_switch_time > 30):
                self.switch_to_best_channel()
                self.mitigation_active = True
        else:
            # If current channel is not jammed, we're good
            if self.mitigation_active:
                logger.info(f"Mitigation no longer needed, current channel {self.current_channel} is stable")
                self.mitigation_active = False

    def switch_to_best_channel(self):
        """Switch to the best available channel based on jamming status"""
        # Get all jammed channels
        jammed_channels = self.get_jammed_channels()
        
        # First try to find a non-jammed primary channel (1, 6, 11)
        available_primary = [ch for ch in PRIMARY_CHANNELS if ch not in jammed_channels]
        
        if available_primary:
            # Choose the primary channel with lowest average power
            best_channel = self.get_lowest_power_channel(available_primary)
            logger.info(f"Switching to non-jammed primary channel {best_channel}")
        else:
            # If all primary channels are jammed, try any non-jammed channel
            available_channels = [ch for ch in WIFI_CHANNELS.keys() if ch not in jammed_channels]
            
            if available_channels:
                best_channel = self.get_lowest_power_channel(available_channels)
                logger.info(f"All primary channels jammed. Switching to channel {best_channel}")
            else:
                # If all channels are jammed, pick the one with the lowest power
                best_channel = self.get_lowest_power_channel(list(WIFI_CHANNELS.keys()))
                logger.warning(f"All channels appear to be jammed! Selecting least jammed channel {best_channel}")
        
        # Perform the channel switch
        self.change_wifi_channel(best_channel)

    def get_lowest_power_channel(self, channel_list):
        """Find the channel with the lowest average power from a list of channels"""
        lowest_power = float('inf')
        best_channel = None
        
        for channel in channel_list:
            if not self.channel_stats[channel]["history"]:
                continue
                
            avg_power = self.channel_stats[channel]["history"][-1]["avg"]
            if avg_power < lowest_power:
                lowest_power = avg_power
                best_channel = channel
        
        # If we couldn't determine (no history data), just pick randomly
        if best_channel is None:
            best_channel = random.choice(channel_list)
            
        return best_channel

    def change_wifi_channel(self, new_channel):
        """Change the WiFi channel (simulated in this implementation)"""
        old_channel = self.current_channel
        self.current_channel = new_channel
        self.channel_switch_time = time.time()
        
        # Record the mitigation action
        self.mitigation_history.append({
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "old_channel": old_channel,
            "new_channel": new_channel,
            "reason": f"Jamming detected on channel {old_channel}"
        })
        
        logger.info(f"MITIGATION: Changed WiFi channel from {old_channel} to {new_channel}")
        
        # In a real implementation, you would execute the actual channel change command here
        # For example, using a command like:
        # subprocess.run(["iwconfig", "wlan0", "channel", str(new_channel)])
        
        # This is a placeholder for the actual implementation
        # The actual command depends on your WiFi hardware, driver, and operating system
        try:
            # Simulate the channel change with a command that would work on Linux
            # On Windows, you would use netsh commands instead
            if os.name == 'nt':  # Windows
                logger.info("Would execute: netsh wlan set channel=? interface=?")
                # Actual command would be something like:
                # subprocess.run(["netsh", "wlan", "set", f"channel={new_channel}", "interface=Wi-Fi"], check=True)
            else:  # Linux/macOS
                logger.info(f"Would execute: iwconfig wlan0 channel {new_channel}")
                # Actual command would be something like:
                # subprocess.run(["iwconfig", "wlan0", "channel", str(new_channel)], check=True)
                
            logger.info("Channel change command would be executed here in a real implementation")
        except Exception as e:
            logger.error(f"Error changing WiFi channel: {str(e)}")

    def get_channel_report(self):
        """Generate channel status report for display"""
        report = []
        for channel, info in WIFI_CHANNELS.items():
            if not self.channel_stats[channel]["history"]:
                report.append(f"Channel {channel}: No data")
                continue
                
            current = self.channel_stats[channel]["history"][-1]
            is_jammed = self.channel_stats[channel]["state"] == "jammed"
            status = "JAMMED" if is_jammed else "Normal"
            
            # Highlight current channel
            if channel == self.current_channel:
                prefix = "→ "  # Arrow indicating current channel
                if is_jammed:
                    color = "\033[93;1m"  # Bold yellow for jammed current channel
                else:
                    color = "\033[94;1m"  # Bold blue for normal current channel
            else:
                prefix = "  "
                color = "\033[91m" if is_jammed else "\033[92m"  # Red for jammed, green for normal
            
            # Format the report line
            report.append(
                f"{prefix}{color}Ch{channel} ({info['center']}MHz): "
                f"Avg={current['avg']:.1f}dBm Max={current['max']:.1f}dBm "
                f"- {status}\033[0m"
            )
        return "\n".join(report)

    def get_mitigation_report(self):
        """Generate a report of mitigation actions taken"""
        if not self.mitigation_history:
            return "No mitigation actions taken yet"
            
        report = ["Recent mitigation actions:"]
        # Show the last 5 mitigation actions
        for action in self.mitigation_history[-5:]:
            report.append(f"{action['timestamp']}: Changed from Ch{action['old_channel']} to Ch{action['new_channel']}")
            
        return "\n".join(report)

    def is_channel_jammed(self, channel):
        """Check if a specific channel is currently jammed"""
        return self.channel_stats[channel]["state"] == "jammed"
    
    def get_jammed_channels(self):
        """Return a list of all currently jammed channels"""
        return [ch for ch, stats in self.channel_stats.items() 
                if stats["state"] == "jammed"]
    
    def stop(self):
        """Stop the detector"""
        logger.info("Stopping jamming detector")
        self.running = False

def clear_screen():
    """Clear terminal screen"""
    os.system('cls' if os.name == 'nt' else 'clear')

def display_realtime(detector):
    """Display real-time analysis with connected line plot"""
    plt.ion()  # Enable interactive mode
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10), gridspec_kw={'height_ratios': [3, 1]})
    
    # Create line plot for spectrum data
    line, = ax1.plot([], [], 'b-', linewidth=1, alpha=0.8, label='Signal')
    
    # Setup spectrum plot
    ax1.set_title("2.4GHz WiFi Spectrum Analysis with Mitigation", fontsize=14, pad=20)
    ax1.set_xlabel("Frequency (MHz)")
    ax1.set_ylabel("Power (dBm)")
    ax1.set_xlim(2400, 2483)
    ax1.set_ylim(-100, -20)
    ax1.grid(True, alpha=0.3)
    
    # Add threshold line
    ax1.axhline(y=JAMMING_THRESHOLD, color='r', linestyle='--', alpha=0.7, 
               label=f'Jamming Threshold ({JAMMING_THRESHOLD} dBm)')
    
    # Add channel markers
    channel_markers = {}
    for ch, info in WIFI_CHANNELS.items():
        # Highlight primary channels differently
        alpha = 0.3 if ch in PRIMARY_CHANNELS else 0.1
        span = ax1.axvspan(info["center"]-11, info["center"]+11, 
                         color=info["color"], alpha=alpha)
        channel_markers[ch] = span
        
        # Only label primary channels to avoid clutter
        if ch in PRIMARY_CHANNELS:
            ax1.text(info["center"], -30, f"Ch{ch}", 
                   ha='center', color=info["color"], weight='bold')
    
    # Setup channel status plot
    ax2.set_title("Channel Status", fontsize=12)
    ax2.set_xlim(0, 14)  # Channels 1-13
    ax2.set_ylim(0, 1)
    ax2.set_xlabel("Channel")
    ax2.set_yticks([])  # No y-ticks needed
    ax2.set_xticks(list(WIFI_CHANNELS.keys()))
    
    # Create initial channel status bars
    channel_bars = {}
    for ch in WIFI_CHANNELS:
        bar = ax2.bar(ch, 1, width=0.8, color='green', alpha=0.7)
        channel_bars[ch] = bar
    
    # Add current channel indicator (will be updated)
    current_ch_indicator = ax2.plot([detector.current_channel], [0.5], 'v', 
                                   color='blue', markersize=10, alpha=0.8)[0]
    
    ax1.legend(loc='upper right')
    plt.tight_layout()
    
    last_graph_update = 0
    
    while detector.running:
        clear_screen()
        print("\n=== WiFi Jamming Detection & Mitigation System ===")
        print(f"Last update: {datetime.fromtimestamp(detector.last_update).strftime('%H:%M:%S')}")
        print(f"Current WiFi channel: {detector.current_channel}")
        
        with detector.lock:
            # Terminal output
            print("\nChannel Status:")
            print(detector.get_channel_report())
            
            jammed_channels = detector.get_jammed_channels()
            if jammed_channels:
                print("\n\033[91m! ! ! JAMMING DETECTED ! ! !\033[0m")
                print(f"Jammed channels: {', '.join(map(str, jammed_channels))}")
                
                if detector.mitigation_active:
                    print("\n\033[93mMitigation active - channel switching enabled\033[0m")
                
                # Beep only if current channel is jammed
                if detector.is_channel_jammed(detector.current_channel):
                    print("\a")  # Beep
            
            # Show mitigation history
            print("\n" + detector.get_mitigation_report())
            
            # Update graph every 2 seconds if we have data
            if time.time() - last_graph_update > 2 and detector.freqs:
                freqs_mhz = np.array(detector.freqs) / 1e6
                powers = np.array(detector.powers)
                
                # Update line plot
                line.set_data(freqs_mhz, powers)
                
                # Dynamic Y-axis scaling
                y_min = min(powers)
                y_max = max(powers)
                ax1.set_ylim(max(-100, y_min-5), min(-20, y_max+10))
                
                # Update channel status bars
                for ch in WIFI_CHANNELS:
                    is_jammed = detector.is_channel_jammed(ch)
                    color = 'red' if is_jammed else 'green'
                    channel_bars[ch][0].set_color(color)
                    
                    # Highlight current channel
                    if ch == detector.current_channel:
                        channel_markers[ch].set_alpha(0.6)  # Make current channel more visible
                    else:
                        alpha = 0.3 if ch in PRIMARY_CHANNELS else 0.1
                        channel_markers[ch].set_alpha(alpha)
                
                # Update current channel indicator
                current_ch_indicator.set_data([detector.current_channel], [0.5])
                
                fig.canvas.draw()
                fig.canvas.flush_events()
                last_graph_update = time.time()
        
        time.sleep(0.5)
    
    plt.ioff()
    plt.close()

def check_hackrf():
    """Check if HackRF is connected and available"""
    try:
        result = subprocess.run(["hackrf_info"], 
                               check=True, 
                               stdout=subprocess.PIPE, 
                               stderr=subprocess.PIPE)
        logger.info("HackRF device detected")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        logger.error("HackRF not detected or hackrf_tools not installed")
        return False

if __name__ == "__main__":
    logger.info("Starting WiFi Jamming Detection & Mitigation System")
    
    # Verify HackRF is connected
    if not check_hackrf():
        print("Error: HackRF not detected or hackrf_tools not installed!")
        exit(1)

    # Create and start the detector
    detector = JammingDetector()
    sweep_thread = threading.Thread(target=detector.run_sweep)
    sweep_thread.daemon = True
    sweep_thread.start()
    
    try:
        display_realtime(detector)
    except KeyboardInterrupt:
        print("\nStopping jamming detection system...")
    except Exception as e:
        logger.error(f"Error in display: {str(e)}")
    finally:
        detector.stop()
        sweep_thread.join(timeout=2)
        logger.info("Jamming detection system stopped")
